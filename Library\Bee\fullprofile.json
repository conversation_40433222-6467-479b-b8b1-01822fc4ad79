{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 34412, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 34412, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 34412, "tid": 3777, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 34412, "tid": 3777, "ts": 1757616740886839, "dur": 967, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 34412, "tid": 3777, "ts": 1757616740890959, "dur": 1308, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 34412, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 34412, "tid": 1, "ts": 1757616740487476, "dur": 3837, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 34412, "tid": 1, "ts": 1757616740491317, "dur": 29834, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 34412, "tid": 1, "ts": 1757616740521160, "dur": 44843, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 34412, "tid": 3777, "ts": 1757616740892517, "dur": 175, "ph": "X", "name": "", "args": {}}, {"pid": 34412, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740486334, "dur": 7915, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740494250, "dur": 384802, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740494999, "dur": 1370, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740496374, "dur": 637, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497013, "dur": 53, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497069, "dur": 496, "ph": "X", "name": "ProcessMessages 2240", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497568, "dur": 134, "ph": "X", "name": "ReadAsync 2240", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497704, "dur": 7, "ph": "X", "name": "ProcessMessages 17612", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497712, "dur": 29, "ph": "X", "name": "ReadAsync 17612", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497743, "dur": 21, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497766, "dur": 21, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497790, "dur": 24, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497817, "dur": 20, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497839, "dur": 22, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497864, "dur": 23, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497889, "dur": 18, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497909, "dur": 23, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497935, "dur": 21, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497958, "dur": 32, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740497993, "dur": 21, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498016, "dur": 21, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498039, "dur": 21, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498063, "dur": 21, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498085, "dur": 20, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498107, "dur": 26, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498136, "dur": 18, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498156, "dur": 25, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498183, "dur": 21, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498206, "dur": 20, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498228, "dur": 24, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498255, "dur": 21, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498279, "dur": 20, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498301, "dur": 25, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498329, "dur": 35, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498367, "dur": 23, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498392, "dur": 40, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498434, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498436, "dur": 23, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498461, "dur": 22, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498486, "dur": 17, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498505, "dur": 18, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498525, "dur": 32, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498560, "dur": 19, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498582, "dur": 29, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498614, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498646, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498648, "dur": 28, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498679, "dur": 22, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498703, "dur": 19, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498725, "dur": 117, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498844, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498867, "dur": 22, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498892, "dur": 21, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498916, "dur": 17, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498935, "dur": 19, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498956, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740498994, "dur": 24, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499020, "dur": 1, "ph": "X", "name": "ProcessMessages 1007", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499021, "dur": 18, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499041, "dur": 16, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499059, "dur": 21, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499082, "dur": 21, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499105, "dur": 20, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499128, "dur": 19, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499149, "dur": 18, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499169, "dur": 21, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499193, "dur": 18, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499213, "dur": 22, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499237, "dur": 21, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499260, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499283, "dur": 21, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499305, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499308, "dur": 27, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499337, "dur": 18, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499358, "dur": 23, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499383, "dur": 20, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499405, "dur": 23, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499431, "dur": 29, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499462, "dur": 28, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499492, "dur": 18, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499512, "dur": 20, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499534, "dur": 32, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499568, "dur": 22, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499592, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499614, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499634, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499656, "dur": 22, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499681, "dur": 19, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499702, "dur": 20, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499724, "dur": 21, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499748, "dur": 21, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499771, "dur": 19, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499793, "dur": 23, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499818, "dur": 19, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499840, "dur": 37, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499880, "dur": 18, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499900, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499920, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499944, "dur": 26, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499973, "dur": 18, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740499993, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500017, "dur": 21, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500040, "dur": 24, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500066, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500088, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500092, "dur": 19, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500113, "dur": 22, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500137, "dur": 21, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500161, "dur": 22, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500185, "dur": 17, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500204, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500231, "dur": 21, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500254, "dur": 18, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500274, "dur": 18, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500294, "dur": 17, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500314, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500338, "dur": 20, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500360, "dur": 21, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500384, "dur": 17, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500404, "dur": 24, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500430, "dur": 22, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500454, "dur": 21, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500477, "dur": 19, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500498, "dur": 17, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500518, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500543, "dur": 21, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500566, "dur": 22, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500590, "dur": 17, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500609, "dur": 120, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500731, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500756, "dur": 20, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500778, "dur": 20, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500800, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500822, "dur": 17, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500841, "dur": 22, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500865, "dur": 21, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500889, "dur": 20, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500910, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500913, "dur": 19, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500934, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500956, "dur": 21, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740500979, "dur": 22, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501003, "dur": 22, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501028, "dur": 17, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501047, "dur": 21, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501070, "dur": 20, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501093, "dur": 21, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501117, "dur": 18, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501137, "dur": 18, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501158, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501177, "dur": 23, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501202, "dur": 20, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501225, "dur": 20, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501247, "dur": 23, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501273, "dur": 21, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501296, "dur": 35, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501332, "dur": 1, "ph": "X", "name": "ProcessMessages 1064", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501333, "dur": 18, "ph": "X", "name": "ReadAsync 1064", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501353, "dur": 18, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501374, "dur": 22, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501398, "dur": 27, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501428, "dur": 21, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501451, "dur": 21, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501474, "dur": 22, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501498, "dur": 21, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501522, "dur": 18, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501542, "dur": 22, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501567, "dur": 23, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501593, "dur": 20, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501616, "dur": 18, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501636, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501659, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501661, "dur": 23, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501687, "dur": 20, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501709, "dur": 19, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501731, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501750, "dur": 21, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501774, "dur": 21, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501798, "dur": 20, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501820, "dur": 17, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501839, "dur": 21, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501862, "dur": 31, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501895, "dur": 21, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501918, "dur": 18, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501939, "dur": 19, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501960, "dur": 21, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740501983, "dur": 21, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502006, "dur": 21, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502030, "dur": 18, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502050, "dur": 20, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502073, "dur": 21, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502096, "dur": 21, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502120, "dur": 21, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502143, "dur": 17, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502163, "dur": 21, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502186, "dur": 22, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502210, "dur": 33, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502247, "dur": 18, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502266, "dur": 25, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502294, "dur": 22, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502318, "dur": 18, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502339, "dur": 19, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502360, "dur": 21, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502383, "dur": 24, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502409, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502410, "dur": 27, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502440, "dur": 19, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502461, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502485, "dur": 22, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502509, "dur": 34, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502545, "dur": 18, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502566, "dur": 16, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502584, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502609, "dur": 23, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502635, "dur": 27, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502664, "dur": 18, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502684, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502707, "dur": 52, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502760, "dur": 1, "ph": "X", "name": "ProcessMessages 1206", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502762, "dur": 18, "ph": "X", "name": "ReadAsync 1206", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502782, "dur": 17, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502801, "dur": 25, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502829, "dur": 21, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502853, "dur": 19, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502874, "dur": 18, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502895, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502920, "dur": 19, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502941, "dur": 27, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502971, "dur": 16, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740502989, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503012, "dur": 22, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503036, "dur": 20, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503058, "dur": 30, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503092, "dur": 17, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503112, "dur": 20, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503147, "dur": 23, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503171, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503172, "dur": 18, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503194, "dur": 21, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503216, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503218, "dur": 24, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503245, "dur": 21, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503269, "dur": 18, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503289, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503312, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503313, "dur": 23, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503338, "dur": 19, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503360, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503382, "dur": 15, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503399, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503424, "dur": 24, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503450, "dur": 78, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503531, "dur": 134, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503667, "dur": 1, "ph": "X", "name": "ProcessMessages 1468", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503670, "dur": 131, "ph": "X", "name": "ReadAsync 1468", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503804, "dur": 37, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503842, "dur": 1, "ph": "X", "name": "ProcessMessages 1699", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503844, "dur": 38, "ph": "X", "name": "ReadAsync 1699", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503884, "dur": 48, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503934, "dur": 30, "ph": "X", "name": "ReadAsync 1322", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503966, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740503991, "dur": 24, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504019, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504021, "dur": 19, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504042, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504068, "dur": 85, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504155, "dur": 1, "ph": "X", "name": "ProcessMessages 2233", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504156, "dur": 18, "ph": "X", "name": "ReadAsync 2233", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504176, "dur": 24, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504203, "dur": 36, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504242, "dur": 18, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504263, "dur": 17, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504282, "dur": 20, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504304, "dur": 29, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504335, "dur": 21, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504358, "dur": 19, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504380, "dur": 19, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504400, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504404, "dur": 23, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504430, "dur": 22, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504454, "dur": 22, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504478, "dur": 20, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504500, "dur": 23, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504525, "dur": 20, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504547, "dur": 22, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504573, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504575, "dur": 23, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504601, "dur": 20, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504623, "dur": 26, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504652, "dur": 19, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504673, "dur": 23, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504699, "dur": 17, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504718, "dur": 21, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504742, "dur": 24, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504769, "dur": 27, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504798, "dur": 17, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504818, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504845, "dur": 23, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504872, "dur": 25, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504900, "dur": 18, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504920, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504922, "dur": 25, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504948, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504950, "dur": 22, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504974, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740504975, "dur": 28, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505005, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505008, "dur": 20, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505031, "dur": 27, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505061, "dur": 25, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505089, "dur": 28, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505119, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505121, "dur": 26, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505148, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505150, "dur": 23, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505177, "dur": 23, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505202, "dur": 28, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505234, "dur": 19, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505255, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505256, "dur": 21, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505280, "dur": 18, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505301, "dur": 19, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505323, "dur": 23, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505349, "dur": 20, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505373, "dur": 61, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505437, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505462, "dur": 22, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505487, "dur": 47, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505537, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505563, "dur": 20, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505587, "dur": 20, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505609, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505611, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505656, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505687, "dur": 21, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505711, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505749, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505750, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505776, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505800, "dur": 51, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505854, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505879, "dur": 21, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505903, "dur": 47, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505952, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740505980, "dur": 19, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506002, "dur": 44, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506049, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506074, "dur": 22, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506100, "dur": 50, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506152, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506153, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506183, "dur": 25, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506212, "dur": 51, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506266, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506291, "dur": 21, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506316, "dur": 41, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506361, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506386, "dur": 22, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506410, "dur": 44, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506457, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506481, "dur": 19, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506502, "dur": 19, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506524, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506562, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506587, "dur": 21, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506611, "dur": 44, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506657, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506679, "dur": 21, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506702, "dur": 45, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506750, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740506775, "dur": 1036, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740507813, "dur": 2, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740507816, "dur": 121, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740507938, "dur": 7, "ph": "X", "name": "ProcessMessages 13691", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740507946, "dur": 25, "ph": "X", "name": "ReadAsync 13691", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740507974, "dur": 23, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740507999, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508001, "dur": 25, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508030, "dur": 23, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508055, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508057, "dur": 18, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508078, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508123, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508151, "dur": 22, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508174, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508176, "dur": 44, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508223, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508246, "dur": 22, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508271, "dur": 19, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508294, "dur": 39, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508336, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508366, "dur": 1, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508368, "dur": 22, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508393, "dur": 35, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508430, "dur": 1, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508432, "dur": 18, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508452, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508497, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508523, "dur": 22, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508549, "dur": 23, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508576, "dur": 25, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508604, "dur": 24, "ph": "X", "name": "ReadAsync 771", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508631, "dur": 19, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508654, "dur": 56, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508712, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508714, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508739, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508742, "dur": 19, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508763, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508766, "dur": 51, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508821, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508845, "dur": 17, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508865, "dur": 19, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508886, "dur": 16, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508905, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508942, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508981, "dur": 1, "ph": "X", "name": "ProcessMessages 1078", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740508984, "dur": 55, "ph": "X", "name": "ReadAsync 1078", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509041, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509066, "dur": 20, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509104, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509107, "dur": 41, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509150, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509177, "dur": 23, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509202, "dur": 19, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509225, "dur": 44, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509271, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509295, "dur": 21, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509317, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509319, "dur": 41, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509362, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509388, "dur": 20, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509412, "dur": 40, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509454, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509478, "dur": 20, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509500, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509502, "dur": 43, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509549, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509579, "dur": 21, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509605, "dur": 35, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509642, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509668, "dur": 22, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509692, "dur": 18, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509713, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509751, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509788, "dur": 24, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509816, "dur": 21, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509841, "dur": 39, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509882, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509910, "dur": 19, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509931, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509932, "dur": 43, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740509978, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510004, "dur": 20, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510028, "dur": 42, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510073, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510099, "dur": 21, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510123, "dur": 18, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510144, "dur": 37, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510183, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510207, "dur": 19, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510230, "dur": 43, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510275, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510314, "dur": 19, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510336, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510338, "dur": 39, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510380, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510404, "dur": 20, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510427, "dur": 61, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510491, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510516, "dur": 19, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510539, "dur": 39, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510580, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510604, "dur": 20, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510627, "dur": 45, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510675, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510699, "dur": 19, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510721, "dur": 43, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510766, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510792, "dur": 20, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510814, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510833, "dur": 38, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510874, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510902, "dur": 20, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510926, "dur": 39, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510967, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740510987, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511012, "dur": 17, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511032, "dur": 40, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511075, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511100, "dur": 20, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511122, "dur": 41, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511165, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511188, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511210, "dur": 20, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511232, "dur": 35, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511270, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511297, "dur": 21, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511321, "dur": 44, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511367, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511391, "dur": 21, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511414, "dur": 45, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511461, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511485, "dur": 21, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511509, "dur": 23, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511534, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511536, "dur": 22, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511560, "dur": 28, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511589, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511592, "dur": 20, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511615, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511660, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511690, "dur": 28, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511722, "dur": 45, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511770, "dur": 131, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511907, "dur": 28, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511936, "dur": 1, "ph": "X", "name": "ProcessMessages 1809", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740511938, "dur": 70, "ph": "X", "name": "ReadAsync 1809", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512011, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512013, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512056, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512083, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512085, "dur": 23, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512109, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512111, "dur": 22, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512137, "dur": 23, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512164, "dur": 26, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512193, "dur": 50, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512247, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512272, "dur": 276, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512551, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512658, "dur": 243, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740512903, "dur": 112, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513019, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513045, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513097, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513099, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513198, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513200, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513225, "dur": 84, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513311, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513313, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513335, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513338, "dur": 100, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513442, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513444, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513468, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513469, "dur": 9, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513482, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513497, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513543, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513569, "dur": 141, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513712, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513714, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513742, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513768, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513771, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513802, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513804, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513848, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513878, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513900, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513966, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740513999, "dur": 96, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514102, "dur": 77, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514183, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514185, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514227, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514250, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514281, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514303, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514338, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514358, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514432, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514443, "dur": 77, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514523, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514550, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514553, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514578, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514604, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514647, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514671, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514687, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514723, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514753, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514755, "dur": 14, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514771, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514816, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514826, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514879, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514899, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514925, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740514939, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515003, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515030, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515049, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515078, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515100, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515102, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515130, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515160, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515196, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515222, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515252, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515272, "dur": 23, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515300, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515314, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515340, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515368, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515382, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515426, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515442, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515483, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515513, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515536, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515558, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515586, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515608, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515629, "dur": 16, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515650, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515675, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515698, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515720, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515749, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515769, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515792, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515800, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515825, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515847, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515849, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515879, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515881, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515907, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515927, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515969, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740515992, "dur": 66, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516061, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516062, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516081, "dur": 4, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516087, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516120, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516151, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516171, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516202, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516222, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516247, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516278, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516301, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516325, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516357, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516379, "dur": 18, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516402, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516430, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516444, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516455, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516489, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516514, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516542, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516569, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516594, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516618, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516632, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516660, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516684, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516710, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516751, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516753, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516778, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516780, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516795, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516815, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516823, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516839, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516862, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516883, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516915, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516932, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516958, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740516972, "dur": 56, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517034, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517058, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517082, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517131, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517147, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517170, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517191, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517211, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517231, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517276, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517338, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517367, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517393, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517394, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517417, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517444, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517469, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517495, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517496, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517527, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517551, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517571, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517602, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517624, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517644, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517680, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517681, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517703, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517704, "dur": 8, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517716, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517740, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517760, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517762, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517781, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517812, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517832, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517862, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517878, "dur": 62, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517943, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517946, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517974, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740517976, "dur": 26, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518004, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518025, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518050, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518076, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518103, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518123, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518148, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518178, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518202, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518221, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518298, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518300, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518333, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518335, "dur": 15, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518353, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518368, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518370, "dur": 20, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518394, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518419, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518450, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518472, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518491, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518513, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518537, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518559, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518581, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518651, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518675, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518677, "dur": 22, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518701, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518731, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518752, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518779, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518813, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518839, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518841, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518864, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518939, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518980, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740518982, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519014, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519016, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519043, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519060, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519078, "dur": 18, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519099, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519128, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519130, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519160, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519183, "dur": 16, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519202, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519238, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519265, "dur": 71, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519340, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519362, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740519364, "dur": 49882, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740569252, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740569254, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740569283, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740569528, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740569567, "dur": 7899, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740577470, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740577471, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740577503, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740577504, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740577580, "dur": 54, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740577636, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740577672, "dur": 118, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740577792, "dur": 400, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740578195, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740578272, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740578298, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740578369, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740578453, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740578473, "dur": 212, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740578687, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740578752, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740578833, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740578941, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740578982, "dur": 78, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740579063, "dur": 157, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740579256, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740579337, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740579339, "dur": 167, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740579626, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740579629, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740579668, "dur": 205, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740579876, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580029, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580106, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580185, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580306, "dur": 80, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580388, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580390, "dur": 85, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580479, "dur": 99, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580582, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580730, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580733, "dur": 34, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580770, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580796, "dur": 27, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580826, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580848, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580945, "dur": 49, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740580995, "dur": 159, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581156, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581158, "dur": 25, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581184, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581185, "dur": 47, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581234, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581305, "dur": 126, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581433, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581547, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581581, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581667, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581691, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581714, "dur": 87, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581804, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581828, "dur": 128, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581959, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740581991, "dur": 79, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740582073, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740582168, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740582242, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740582376, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740583990, "dur": 77, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740584071, "dur": 1059, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585185, "dur": 41, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585228, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585230, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585251, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585364, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585366, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585388, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585408, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585486, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585563, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585564, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585591, "dur": 189, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585784, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585805, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585868, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585957, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740585976, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740586019, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740586112, "dur": 363, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740586479, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740586592, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740586594, "dur": 55977, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740642581, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740642585, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740642608, "dur": 1255, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740643871, "dur": 11538, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740655418, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740655421, "dur": 843, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740656269, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740656272, "dur": 94, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740656370, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740656372, "dur": 219, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740656597, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740656698, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740656700, "dur": 583, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740657287, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740657327, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740657329, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740657388, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740657468, "dur": 198, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740657670, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740657698, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740657700, "dur": 232, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740657936, "dur": 428, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740658368, "dur": 210, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740658609, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740658725, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740658773, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740658775, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740658839, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740658842, "dur": 512, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740659357, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740659453, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740659471, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740659614, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740659733, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740659735, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740659862, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740659906, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740660067, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740660228, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740660230, "dur": 73, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740660306, "dur": 303, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740660614, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740660653, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740660810, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740660876, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740660878, "dur": 220, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661102, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661145, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661242, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661277, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661308, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661396, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661398, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661444, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661483, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661589, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661591, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661647, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661676, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661678, "dur": 45, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661725, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661795, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661834, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661835, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661872, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661931, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661991, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740661993, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662026, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662060, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662093, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662133, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662172, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662173, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662204, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662249, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662283, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662285, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662330, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662456, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662459, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662496, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662498, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662528, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662530, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662562, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662594, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662596, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662645, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662676, "dur": 35, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662714, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662716, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662757, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662793, "dur": 86, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662881, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662883, "dur": 25, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740662972, "dur": 71, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663046, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663077, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663079, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663111, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663209, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663240, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663242, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663268, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663397, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663399, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663434, "dur": 72, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663510, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663537, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663562, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663589, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663684, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663722, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663723, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663762, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663854, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663866, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663907, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740663980, "dur": 20, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740664003, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740664089, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740664112, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740664133, "dur": 226, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740664363, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740664384, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740664405, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740664451, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740664468, "dur": 67231, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740731712, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740731718, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740731788, "dur": 20, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740731844, "dur": 6991, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740738842, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740738844, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740738886, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740738888, "dur": 20118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740759018, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740759021, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740759064, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740759068, "dur": 64, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740759135, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740759137, "dur": 68807, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740827956, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740827961, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740827986, "dur": 22, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740828009, "dur": 8514, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740836533, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740836537, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740836558, "dur": 94, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740836653, "dur": 23192, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740859855, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740859859, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740859897, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740859901, "dur": 1711, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740861617, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740861694, "dur": 1525, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740863222, "dur": 5421, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740868647, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740868648, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740868722, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740868726, "dur": 689, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740869419, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740869445, "dur": 431, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 34412, "tid": 12884901888, "ts": 1757616740869879, "dur": 8435, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 34412, "tid": 3777, "ts": 1757616740892807, "dur": 18442, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 34412, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 34412, "tid": 8589934592, "ts": 1757616740484684, "dur": 81363, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 34412, "tid": 8589934592, "ts": 1757616740566050, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 34412, "tid": 8589934592, "ts": 1757616740566055, "dur": 781, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 34412, "tid": 3777, "ts": 1757616740911251, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 34412, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 34412, "tid": 4294967296, "ts": 1757616740468514, "dur": 411239, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 34412, "tid": 4294967296, "ts": 1757616740470882, "dur": 9466, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 34412, "tid": 4294967296, "ts": 1757616740879900, "dur": 4073, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 34412, "tid": 4294967296, "ts": 1757616740881916, "dur": 87, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 34412, "tid": 4294967296, "ts": 1757616740884055, "dur": 11, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 34412, "tid": 3777, "ts": 1757616740911263, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1757616740494007, "dur": 2269, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757616740496283, "dur": 810, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757616740497207, "dur": 65, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1757616740497272, "dur": 1011, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757616740498961, "dur": 136, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_01B6FF8ED20C5564.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1757616740509242, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1757616740513154, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1757616740498327, "dur": 15293, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757616740513631, "dur": 356474, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757616740870107, "dur": 154, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757616740870277, "dur": 239, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757616740870704, "dur": 57, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757616740870783, "dur": 1498, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1757616740497745, "dur": 15894, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740513660, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740513851, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_C784423AFBE2D728.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1757616740514339, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740514735, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_C14419526F42C717.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1757616740515061, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740515383, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_C0A03C5C365EC782.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1757616740515642, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740516023, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_65E1349D57A0F795.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1757616740516256, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740516524, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_A4CBDFDF656AC5D6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1757616740516765, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740517066, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740517343, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740517631, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740517921, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740518183, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740518496, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740518860, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740519093, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740519369, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740519628, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740519910, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740520166, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740520417, "dur": 4821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740525239, "dur": 4791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740530030, "dur": 4643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740537336, "dur": 615, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Drawing\\Manipulators\\MasterPreviewManipulator.cs"}}, {"pid": 12345, "tid": 1, "ts": 1757616740534673, "dur": 5324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740539997, "dur": 4579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740544576, "dur": 4669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740549245, "dur": 5228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740554474, "dur": 4738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740559212, "dur": 4745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740563957, "dur": 5032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740568989, "dur": 4883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740573873, "dur": 4501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740578374, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740578936, "dur": 693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740579629, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740580249, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1757616740580465, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740580518, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1757616740581297, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740581540, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740581620, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740581792, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740582076, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740582243, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740582444, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1757616740582665, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1757616740583169, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740583424, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740583838, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740583978, "dur": 71312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740655300, "dur": 3678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1757616740658979, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740659052, "dur": 4013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1757616740663065, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740663265, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740663370, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740663505, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740663690, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740663842, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740663977, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740664041, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740664273, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740664673, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757616740665494, "dur": 204675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740497746, "dur": 15905, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740513662, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740513977, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_11AE432291B3EF2E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757616740514173, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740514519, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_2AFBFF2A9F88B741.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757616740514900, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740515216, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_172E4090B2567F92.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757616740515553, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740515805, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_C6818E2D05AB9635.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757616740516142, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740516407, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_3AC38595511E6EA2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757616740516667, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740516970, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740517292, "dur": 61436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1757616740578729, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740578956, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757616740579057, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1757616740579463, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740579669, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757616740579763, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1757616740579974, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740580170, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757616740580237, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740580353, "dur": 805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1757616740581158, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740581320, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757616740581489, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740581610, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1757616740581953, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740582308, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740582404, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740582472, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740583138, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740583835, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740583962, "dur": 72693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740656661, "dur": 1837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1757616740658499, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740658710, "dur": 2649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1757616740661360, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740661482, "dur": 3295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1757616740664779, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740664935, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757616740664998, "dur": 205362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740497797, "dur": 15861, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740513666, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_EFED20067D688B06.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757616740514042, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740514388, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_AA2C48B7A9DF071B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757616740514752, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740515081, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_D3A2198C6EF802F2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757616740515426, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740515656, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_EB272D7CA24C7020.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757616740516024, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740516271, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_26E42AFAAC5803E9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757616740516536, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740516761, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740517084, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740517369, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740517664, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740517954, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740518260, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740518571, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740518930, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740519165, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740519416, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740519634, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740519920, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740520178, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740520434, "dur": 4825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740525259, "dur": 4766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740530026, "dur": 4652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740535633, "dur": 681, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Drawing\\Inspector\\PropertyDrawers\\TriplanarNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1757616740534678, "dur": 5254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740539932, "dur": 4570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740544502, "dur": 4616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740549118, "dur": 5208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740554327, "dur": 4747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740559075, "dur": 4794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740563869, "dur": 4956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740568826, "dur": 4831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740573678, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740574157, "dur": 4472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740578630, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740578935, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740579632, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740580266, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757616740580563, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740580666, "dur": 1427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1757616740582094, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740582457, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740582512, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757616740582660, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740582730, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1757616740583237, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740583531, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740583805, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740583951, "dur": 73676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740657628, "dur": 4253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1757616740661882, "dur": 835, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740662724, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740662893, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740662982, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740663118, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740663173, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740663343, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740663512, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740663637, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740663753, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740663839, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740663944, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740664028, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740664115, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740664246, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740664384, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740664436, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740664524, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740665278, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757616740665341, "dur": 204974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740498050, "dur": 16005, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740514057, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_2D18E38FE423FFB2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1757616740514518, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740514893, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_720879377B5C05F3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1757616740515210, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740515625, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_A1ECBE3F47CFCD22.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1757616740516016, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740516247, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_25A46DF1B257E635.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1757616740516488, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740516703, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740516960, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740517285, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740517540, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740517844, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740518116, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740518382, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740518750, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740519020, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740519275, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740519513, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740519730, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740520030, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740520278, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740520494, "dur": 5018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740525512, "dur": 4693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740530205, "dur": 4635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740535506, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Data\\Nodes\\Procedural\\Noise\\VoronoiNode.cs"}}, {"pid": 12345, "tid": 4, "ts": 1757616740534840, "dur": 5253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740540093, "dur": 4500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740544593, "dur": 4616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740549210, "dur": 5135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740554345, "dur": 4736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740559081, "dur": 4750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740563831, "dur": 5019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740568851, "dur": 4793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740573644, "dur": 4700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740578344, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740578964, "dur": 673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740579637, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740580278, "dur": 1541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740581820, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740582277, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740582457, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740583141, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740583802, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740583934, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1757616740584038, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1757616740584278, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740584471, "dur": 70670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740655151, "dur": 3601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1757616740658753, "dur": 550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740659307, "dur": 4761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1757616740664069, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740664436, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740664515, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740665031, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757616740665087, "dur": 205261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740497833, "dur": 15843, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740513684, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_91DA264BCA3B6154.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757616740514416, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740514784, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_EA502D872F709377.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757616740515107, "dur": 375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740515489, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_50CD8103462AD7C6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757616740515732, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740516086, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2431D0E14C37672D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757616740516316, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740516565, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757616740516812, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740517097, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740517376, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740517676, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740517976, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740518257, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740518572, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740518989, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740519275, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740519536, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740519757, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740520034, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740520302, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740520559, "dur": 4925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740525484, "dur": 4705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740530190, "dur": 4706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740537355, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Data\\Nodes\\Input\\Matrix\\Matrix4Node.cs"}}, {"pid": 12345, "tid": 5, "ts": 1757616740534896, "dur": 5399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740540295, "dur": 4573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740544868, "dur": 4665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740549533, "dur": 5167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740554701, "dur": 4759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740559460, "dur": 4729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740564190, "dur": 4994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740569184, "dur": 4837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740574021, "dur": 4585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740578606, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740578976, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740579635, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740580243, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757616740580580, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740580655, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757616740580777, "dur": 1053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1757616740581831, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740582226, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740582321, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740582463, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740583141, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740583815, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740583942, "dur": 3340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740587283, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757616740587410, "dur": 67576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740654994, "dur": 2538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1757616740657534, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740657780, "dur": 5955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1757616740663735, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740663983, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740664112, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740664258, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740664486, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740664771, "dur": 989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757616740665794, "dur": 204359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740497855, "dur": 15869, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740513730, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_CD14317997D63177.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757616740514338, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740514772, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_7681D4B2DD45B37D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757616740515133, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740515525, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_A06C5659376121A9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757616740515729, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740516050, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_D8F37AF5E158D413.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757616740516295, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740516552, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757616740516760, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740517085, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740517421, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740517741, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740518002, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740518292, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740518586, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740518943, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740519179, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740519436, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740519656, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740519951, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740520213, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740520468, "dur": 4912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740525381, "dur": 4760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740530141, "dur": 4657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740537391, "dur": 613, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Data\\Nodes\\UV\\PolarCoordinatesNode.cs"}}, {"pid": 12345, "tid": 6, "ts": 1757616740534798, "dur": 5251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740540050, "dur": 4557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740544608, "dur": 4659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740549267, "dur": 5159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740554426, "dur": 4665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740559091, "dur": 4764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740563855, "dur": 5018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740568873, "dur": 4911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740573784, "dur": 4716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740578500, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740578907, "dur": 710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740579617, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740580266, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757616740580585, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740580696, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1757616740581429, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740581611, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740581674, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757616740581773, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740581943, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1757616740582153, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740582495, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740583152, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740583795, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757616740583974, "dur": 1115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1757616740585089, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740585326, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740585406, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757616740585507, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1757616740585857, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740585991, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757616740586089, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1757616740586343, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740586503, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757616740586598, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1757616740586757, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740586890, "dur": 68117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740655015, "dur": 1575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1757616740656592, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740656729, "dur": 5270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1757616740662000, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740662165, "dur": 3104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.Entities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1757616740665269, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757616740665393, "dur": 204889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740498026, "dur": 16093, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740514122, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D886B18B9CD470CB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1757616740514586, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740515016, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_B1E8C5601C7C653C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1757616740515305, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740515573, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7B9B9DF33CD70766.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1757616740515816, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740516134, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_6AE46EF62F6F0F68.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1757616740516376, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740516613, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740516851, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740517179, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740517465, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740517792, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740518067, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740518327, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1757616740518384, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740518752, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740519029, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740519293, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740519559, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740519774, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740520101, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740520368, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740520706, "dur": 4819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740525525, "dur": 4689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740530215, "dur": 4676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740537335, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Data\\Nodes\\Input\\Texture\\SampleTexture2DArrayNode.cs"}}, {"pid": 12345, "tid": 7, "ts": 1757616740534892, "dur": 5281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740540173, "dur": 4530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740544704, "dur": 4647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740549351, "dur": 5272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740554623, "dur": 4645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740559269, "dur": 4784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740564053, "dur": 4954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740569007, "dur": 4829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740573836, "dur": 4687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740578523, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740578916, "dur": 772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740579688, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740580282, "dur": 1541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740581823, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740582236, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740582448, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740582515, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1757616740582677, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740582805, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1757616740583221, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740583417, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740583825, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740583947, "dur": 71033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740654983, "dur": 2876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1757616740657859, "dur": 841, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740658706, "dur": 4694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1757616740663401, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740663740, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740663872, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740663983, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740664043, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740664295, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740664531, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740665295, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757616740665371, "dur": 204930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740497902, "dur": 15851, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740513757, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_516D101FBF22CB69.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1757616740514096, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740514469, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_9FCBBE13E57027BD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1757616740514859, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740515138, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_20317D38C7F02261.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1757616740515520, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740515711, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_4C2196917D3B3D54.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1757616740516086, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740516404, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A9106CE4EB5035DF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1757616740516644, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740516889, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_CEF96CF5640B5F47.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1757616740517179, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740517455, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740517781, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740518036, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740518314, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740518665, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740518976, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740519189, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740519429, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740519658, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740520018, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740520281, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740520523, "dur": 5020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740525543, "dur": 4784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740530327, "dur": 4639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740536028, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Data\\Nodes\\FormerNameAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1757616740534966, "dur": 5155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740540121, "dur": 4554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740544676, "dur": 4574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740549251, "dur": 5105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740554357, "dur": 4659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740559016, "dur": 4753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740563769, "dur": 4907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740568676, "dur": 4825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740573501, "dur": 4505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740578145, "dur": 815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740578960, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740579630, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740580279, "dur": 1552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740581831, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740582237, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740582507, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740583150, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740583834, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740583961, "dur": 72801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740656769, "dur": 6042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1757616740662812, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740662972, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740663042, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740663117, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740663191, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740663320, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740663485, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740663578, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740663902, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740664039, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740664180, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740664310, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740664514, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757616740664990, "dur": 205109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740497923, "dur": 15895, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740513835, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_14804BCC10C51040.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757616740514429, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740514902, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_DDB94F831A1EA5CD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757616740515307, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740515575, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_E40908218B74D267.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757616740515886, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740516190, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_5BEEEDA9215058EF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757616740516438, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740516663, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_CA19911ED8F920A1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757616740516903, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740517211, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_4A87A716FBA7BB4C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757616740517495, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740517812, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740518092, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740518343, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740518697, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740518985, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740519223, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740519473, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740519697, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740519991, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740520294, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740520531, "dur": 4910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740525441, "dur": 4730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740530171, "dur": 4670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740534841, "dur": 5243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740540084, "dur": 4580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740544664, "dur": 4653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740549317, "dur": 5071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740554389, "dur": 4698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740559088, "dur": 4877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740563966, "dur": 4931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740568897, "dur": 4898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740573795, "dur": 4683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740578479, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740578942, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740579625, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740580153, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757616740580491, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740580554, "dur": 1055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757616740581610, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740581915, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740582039, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757616740582107, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740582239, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757616740582569, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740582882, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740583007, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757616740583105, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740583163, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757616740583502, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740583725, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740583809, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740583941, "dur": 2834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740586776, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757616740586877, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757616740587066, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740587153, "dur": 67842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740655004, "dur": 1589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1757616740656595, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740656762, "dur": 4215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1757616740660977, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740661405, "dur": 3777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1757616740665182, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757616740665309, "dur": 205020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740498094, "dur": 15954, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740514053, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_68197AA3794CFDC7.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757616740514380, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740514746, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_F696DB38B6FE8483.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757616740515064, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740515416, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_71C02C7BDD17B14E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757616740515653, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740516028, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_9A02A733FF053DF0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757616740516287, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740516538, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2F018D732F244A87.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757616740516757, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740517058, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740517337, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740517628, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740517911, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740518168, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740518501, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740518908, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740519153, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740519403, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740519619, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740519899, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740520190, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740520442, "dur": 4890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740525333, "dur": 4777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740530110, "dur": 4608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740537399, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Drawing\\Inspector\\PropertyDrawers\\ColorPropertyDrawer.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757616740534718, "dur": 5157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740539875, "dur": 4589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740544464, "dur": 4740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740549205, "dur": 5132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740554337, "dur": 4708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740559045, "dur": 4750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740563795, "dur": 4993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740568789, "dur": 4906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740573695, "dur": 4709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740578405, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740578896, "dur": 709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740579655, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740580214, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757616740580666, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740581211, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1757616740581866, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740582257, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740582343, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757616740582448, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740582520, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1757616740582992, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740583362, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740583830, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740583964, "dur": 72682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740656655, "dur": 4024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1757616740660680, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740660902, "dur": 4294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1757616740665196, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740665292, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757616740665351, "dur": 204957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740497963, "dur": 15980, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740514033, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_ABBC145B2298795A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757616740514376, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740514750, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_8E006F2194C60093.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757616740515068, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740515414, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_5BC85239AC343D49.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757616740515647, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740516110, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_01B6FF8ED20C5564.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757616740516352, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740516609, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_F6EE2176487706CE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757616740516824, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740517127, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740517412, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740517750, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740518068, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740518362, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740518705, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740518989, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740519236, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740519515, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740519739, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740520020, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740520270, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740520489, "dur": 4972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740525461, "dur": 4657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740530119, "dur": 4619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740537368, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Drawing\\Controls\\EnumConversionControl.cs"}}, {"pid": 12345, "tid": 11, "ts": 1757616740534738, "dur": 5326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740540064, "dur": 4521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740544586, "dur": 4628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740549215, "dur": 5144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740554359, "dur": 4671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740559031, "dur": 4820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740563852, "dur": 4969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740568822, "dur": 4877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740573699, "dur": 4758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740578457, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740578953, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740579690, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740580246, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757616740580657, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740580724, "dur": 1083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1757616740581808, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740582359, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740582447, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757616740582605, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1757616740582938, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740583112, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740583787, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740583936, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757616740584047, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1757616740584289, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740584446, "dur": 70709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740655172, "dur": 6291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1757616740661465, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740661961, "dur": 3420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1757616740665382, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757616740665490, "dur": 204748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740497985, "dur": 15883, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740513873, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_E0002C7E987E543F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757616740514055, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740514409, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_B38AE2BA66ED2BCF.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757616740514759, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740515084, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_CD1B1983351D4CF4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757616740515435, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740515661, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_58219533EB200AA1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757616740516030, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740516316, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_23A0021F04E7F741.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757616740516569, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740516811, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740517099, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740517401, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740517687, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740517972, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740518256, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740518539, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740518925, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740519162, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740519411, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740519651, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740519936, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740520205, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740520494, "dur": 5090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740525584, "dur": 4770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740530355, "dur": 4624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740534979, "dur": 5214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740540193, "dur": 4603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740544797, "dur": 4609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740549407, "dur": 5114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740554522, "dur": 4726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740559249, "dur": 4800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740564050, "dur": 4953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740569003, "dur": 4836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740573839, "dur": 4680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740578519, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740578908, "dur": 709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740579617, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740580135, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757616740580206, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740580267, "dur": 1401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1757616740581670, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740582060, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740582208, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757616740582280, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740582401, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1757616740582882, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740583104, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757616740583235, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1757616740583725, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740583966, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757616740584080, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1757616740584368, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740584520, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757616740584618, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1757616740584992, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740585133, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757616740585239, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1757616740585425, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740585567, "dur": 69481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740655060, "dur": 5886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1757616740660947, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740661221, "dur": 4405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1757616740665627, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740665760, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757616740665825, "dur": 204313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740497812, "dur": 15856, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740513675, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_4FE6CB3F90BF25DB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757616740514216, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740514521, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_99EF41B248E852FD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757616740515019, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740515314, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_4E71B40F0A8F8C5E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757616740515602, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740516004, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_6AC3A29253D442E6.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757616740516294, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740516666, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740516959, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740517244, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_33352EA77CF2B9C9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757616740517521, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740517811, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740518078, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740518377, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740518745, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740519015, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740519250, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740519489, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740519719, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740519993, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740520248, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740520529, "dur": 4926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740525456, "dur": 4862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740530318, "dur": 4641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740537385, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Data\\Nodes\\Input\\Geometry\\BitangentVectorNode.cs"}}, {"pid": 12345, "tid": 13, "ts": 1757616740534959, "dur": 5303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740540262, "dur": 4576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740544838, "dur": 4744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740549583, "dur": 5203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740554786, "dur": 4730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740559516, "dur": 4776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740564292, "dur": 4997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740569289, "dur": 4796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740574085, "dur": 4528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740578613, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740578982, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740579634, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740580343, "dur": 1464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740581808, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740582207, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757616740582277, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740582353, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1757616740582831, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740583153, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740583231, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740583790, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740583938, "dur": 1197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740585136, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757616740585204, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740585273, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1757616740586037, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740586172, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757616740586272, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1757616740586637, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740586772, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757616740586867, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1757616740587142, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740587277, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757616740587382, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1757616740588711, "dur": 144316, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1757616740739965, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 13, "ts": 1757616740739577, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1757616740740101, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740740188, "dur": 17973, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1757616740740186, "dur": 19036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1757616740760051, "dur": 119, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757616740760996, "dur": 76866, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1757616740869865, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1757616740869838, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1757616740870005, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740498130, "dur": 15916, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740514047, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_F9E486C685F6763C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1757616740514342, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740514632, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DC300FC5E8D775FE.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1757616740515050, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740515369, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_F098A868119F324A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1757616740515649, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740516104, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_C49FA85456E8A332.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1757616740516339, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740516621, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740516867, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740517165, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740517448, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740517731, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740518029, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740518302, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740518626, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1757616740518737, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740519036, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740519296, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740519548, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740519763, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740520039, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740520335, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740520560, "dur": 4845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740525405, "dur": 4709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740530115, "dur": 4643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740534758, "dur": 5125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740539883, "dur": 4585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740544468, "dur": 4696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740549165, "dur": 5213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740554379, "dur": 4655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740559035, "dur": 4855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740563891, "dur": 4971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740568863, "dur": 4878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740573741, "dur": 4742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740578483, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740578899, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740579619, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740580150, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1757616740580236, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740580681, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1757616740581407, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740581568, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740581826, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740582278, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740582483, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740583162, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740583833, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740583958, "dur": 72850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740656816, "dur": 6296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1757616740663113, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740663350, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740663507, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740663681, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740663789, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740663897, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740663988, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740664073, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740664197, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740664388, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740664469, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740664569, "dur": 789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757616740665403, "dur": 204872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740497873, "dur": 15859, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740513737, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_49F469B76C684FA5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1757616740514432, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740514844, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_C3623301B1B89005.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1757616740515113, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740515505, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_6482C21E3A21D3F8.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1757616740515683, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740516088, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_1C27E112859BD998.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1757616740516311, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740516686, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740516961, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740517302, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740517616, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740517910, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740518175, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740518492, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740518874, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740519103, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740519379, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740519635, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740519924, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740520202, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740520449, "dur": 4858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740525308, "dur": 4700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740530009, "dur": 4577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740534587, "dur": 4942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740539529, "dur": 4527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740544057, "dur": 4552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740548610, "dur": 5055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740553666, "dur": 4736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740558403, "dur": 4819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740563223, "dur": 4918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740568141, "dur": 4852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740572994, "dur": 4764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740577758, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740578740, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740578927, "dur": 696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740579623, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740580288, "dur": 1524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740581813, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740582210, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1757616740582283, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740582373, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1757616740582850, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740583079, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740583151, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740583828, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740583955, "dur": 72862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740656832, "dur": 5679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1757616740662512, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740662728, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740662782, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740662847, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 15, "ts": 1757616740662898, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740663030, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740663107, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740663185, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740663328, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740663446, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740663545, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740663647, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740663734, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740663864, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740663942, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740664034, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740664250, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740664413, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740664740, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740664840, "dur": 196224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757616740861133, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1757616740861066, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1757616740861246, "dur": 1804, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1757616740863051, "dur": 7064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740498176, "dur": 15840, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740514016, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_F0C3B445B31A571D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757616740514248, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740514539, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_644337875AD018A6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757616740514923, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740515328, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_1ADF9FE2E7C5229F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757616740515608, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740515939, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_925AE7006CF64150.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757616740516222, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740516476, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740516701, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740516958, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740517249, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_A5FBD79E52C0F38C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757616740517538, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740517831, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740518096, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740518383, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740518800, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740519044, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740519277, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740519564, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740519814, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740520096, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740520364, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740520597, "dur": 4331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740524929, "dur": 4764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740529693, "dur": 4603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740537324, "dur": 540, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Generation\\Enumerations\\NormalDropOffSpace.cs"}}, {"pid": 12345, "tid": 16, "ts": 1757616740534296, "dur": 5246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740539542, "dur": 4564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740544106, "dur": 4569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740548675, "dur": 5090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740553765, "dur": 4723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740558488, "dur": 4828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740563316, "dur": 4901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740580567, "dur": 251, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 16, "ts": 1757616740580818, "dur": 890, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 16, "ts": 1757616740581709, "dur": 360, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 16, "ts": 1757616740568217, "dur": 13855, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740582072, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740582248, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740582454, "dur": 653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740583107, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740583847, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740583968, "dur": 71392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740655369, "dur": 4507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1757616740659877, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740660098, "dur": 4529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1757616740664627, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740664771, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740664850, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757616740664952, "dur": 205148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740497951, "dur": 15901, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740513864, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_EBB61CDB4B54F2BC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757616740514418, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740514776, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_42C0614AAF9B8C6A.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757616740515103, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740515435, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_08E54A91B6A71EA8.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757616740515730, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740516020, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_EFFCEFF89F2BA0FB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757616740516249, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740516506, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_16E38068364D2C9C.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757616740516722, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740517015, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740517299, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740517625, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740517922, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740518193, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740518493, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740518870, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740519109, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740519361, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740519593, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740519829, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740520104, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740520378, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740520603, "dur": 4945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740525549, "dur": 4726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740530276, "dur": 4611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740534888, "dur": 5267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740540155, "dur": 4589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740544745, "dur": 4584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740549329, "dur": 5170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740554500, "dur": 4649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740559149, "dur": 4837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740563987, "dur": 4923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740568910, "dur": 4858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740573768, "dur": 4731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740578500, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740578901, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740579615, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740580144, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757616740580217, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740580295, "dur": 1182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1757616740581478, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740581794, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740581908, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740582229, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740582319, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740582463, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740583164, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740583849, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740583972, "dur": 71461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740655442, "dur": 3697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1757616740659139, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740659214, "dur": 4763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1757616740663979, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740664243, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740664507, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740664939, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757616740665223, "dur": 205110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740498113, "dur": 15920, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740514033, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7C4A0B3B3F1B20B1.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757616740514292, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740514623, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FD010C7C709FE9F3.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757616740515012, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740515300, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_50BEA2D7BCDEB21E.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757616740515594, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740515893, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_0D5E43716DA6FCA7.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757616740516141, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740516382, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_AD9C94B5E776AAC6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757616740516637, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740516869, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740517202, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_290CD8B1B834DFD8.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757616740517489, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740517840, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740518137, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740518380, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740518753, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740519056, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740519313, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740519561, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740519795, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740520082, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740520336, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740520605, "dur": 5010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740525615, "dur": 4700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740530315, "dur": 4710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740535025, "dur": 4744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740539769, "dur": 4562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740544331, "dur": 4662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740548994, "dur": 5052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740554047, "dur": 4685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740558733, "dur": 4882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740563615, "dur": 4950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740568565, "dur": 4823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740573388, "dur": 4764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740578153, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740578935, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740579624, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740580296, "dur": 1510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740581807, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740582209, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757616740582335, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740582467, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1757616740582998, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740583283, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757616740583423, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1757616740583735, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740583867, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740583938, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757616740584086, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1757616740584438, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740584595, "dur": 70532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740655140, "dur": 4938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1757616740660078, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740660189, "dur": 4109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1757616740664298, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740664614, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740664701, "dur": 1035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757616740665774, "dur": 204386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740498005, "dur": 15941, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740513949, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_35032ED94660F120.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757616740514481, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740514861, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_F7402F24E8D87867.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757616740515125, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740515513, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_10BC93FCF4D49257.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757616740515697, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740516022, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_2B7140413B78E93D.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757616740516266, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740516527, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_DF314A1AEFE541E6.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757616740516736, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740517051, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740517328, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740517615, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740517900, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740518158, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740518443, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740518819, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740519088, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740519352, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740519606, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740519855, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740520136, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740520388, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740520623, "dur": 4728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740525352, "dur": 4694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740530046, "dur": 4580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740534626, "dur": 4798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740539425, "dur": 4524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740543950, "dur": 4650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740548601, "dur": 5133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740553734, "dur": 4696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740558431, "dur": 4751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740563183, "dur": 4962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740568145, "dur": 4993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740573139, "dur": 4768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740577908, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740578156, "dur": 738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740578894, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740579620, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740580149, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757616740580241, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740580388, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1757616740581205, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740581392, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757616740581509, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740581624, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1757616740582244, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740582496, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740583145, "dur": 676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740583821, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740583951, "dur": 73012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740656970, "dur": 4386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1757616740661357, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740661473, "dur": 3717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1757616740665190, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757616740665310, "dur": 205011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740498156, "dur": 15863, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740514019, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_03374CA759E3799C.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1757616740514319, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740514625, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_D2315CB7E7EE7CA2.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1757616740515035, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740515387, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_A5F644CC105E68D5.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1757616740515618, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740515951, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_542D2B85C6433138.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1757616740516229, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740516505, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740516726, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740516991, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740517284, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740517540, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740517866, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740518126, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740518370, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740518716, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740519004, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740519276, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740519528, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740519748, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740520035, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740520292, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740520518, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Localization.Abstractions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1757616740520518, "dur": 5086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740525605, "dur": 4828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740530433, "dur": 4624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740535540, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Data\\Interfaces\\Graph\\DrawState.cs"}}, {"pid": 12345, "tid": 20, "ts": 1757616740537340, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Data\\Implementation\\GraphObject.cs"}}, {"pid": 12345, "tid": 20, "ts": 1757616740535057, "dur": 5320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740540377, "dur": 4575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740544952, "dur": 4777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740549730, "dur": 5153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740554883, "dur": 4687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740559570, "dur": 4784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740564355, "dur": 4986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740569342, "dur": 4891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740574233, "dur": 4446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740578679, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740578931, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740579621, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740580248, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1757616740580535, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1757616740581312, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740581780, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740581941, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740582250, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740582460, "dur": 686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740583146, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740583823, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740583949, "dur": 71060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740655011, "dur": 1580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1757616740656592, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740656863, "dur": 4324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1757616740661188, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740661495, "dur": 3362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1757616740664857, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740664937, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757616740665000, "dur": 205355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740498069, "dur": 15968, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740514049, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_7F5F36324633F72A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1757616740514372, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740514645, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_04F01FBC7128CA00.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1757616740515055, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740515374, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_59BF70260A749F2D.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1757616740515617, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740515945, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_ED44CE8AEC265254.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1757616740516228, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740516483, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740516720, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740516975, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740517288, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740517601, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740517873, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740518145, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740518440, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740518821, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740519061, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740519314, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740519562, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740519801, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740520087, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740520358, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740520593, "dur": 4833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740525427, "dur": 4697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740530124, "dur": 4648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740537308, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Data\\Util\\PrecisionUtil.cs"}}, {"pid": 12345, "tid": 21, "ts": 1757616740534773, "dur": 4990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740539763, "dur": 4558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740544321, "dur": 4663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740548984, "dur": 5110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740554094, "dur": 4661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740558755, "dur": 4772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740563528, "dur": 4914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740568443, "dur": 4905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740573349, "dur": 4800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740578150, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740578898, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740579661, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740580290, "dur": 1509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740581799, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740582223, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740582313, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740582510, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740583140, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740583799, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740583939, "dur": 2055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740585995, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1757616740586062, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740586120, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1757616740586335, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740586515, "dur": 68501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740655026, "dur": 1575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1757616740656603, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740656733, "dur": 3484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1757616740660218, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740660410, "dur": 4273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1757616740664684, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757616740664893, "dur": 205209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740498193, "dur": 15835, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740514029, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_E3649F188462AF39.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1757616740514291, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740514611, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_124E52F5AEB86DF9.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1757616740515033, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740515322, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_D88B570BEB2D2B6B.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1757616740515596, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740515899, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_806E4188F6D939E2.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1757616740516181, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740516430, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_1203038D25BDCEC8.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1757616740516657, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740516899, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740517201, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740517497, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740517843, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740518121, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740518387, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740518846, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740519109, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740519361, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740519601, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740519902, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740520152, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740520389, "dur": 4809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740525199, "dur": 4666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740529865, "dur": 4574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740534439, "dur": 5361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740539800, "dur": 4584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740544384, "dur": 4572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740548956, "dur": 5073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740554030, "dur": 4690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740558720, "dur": 4839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740563559, "dur": 4895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740568454, "dur": 4863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740573317, "dur": 4812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740578148, "dur": 744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740578892, "dur": 767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740579659, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740580275, "dur": 1495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740581772, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740581909, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740582242, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740582455, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740583128, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1757616740583277, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740583359, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1757616740583648, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740583848, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740583965, "dur": 71709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740655680, "dur": 2836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1757616740658517, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740658637, "dur": 3557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1757616740662195, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740662461, "dur": 3137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Settings.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1757616740665599, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740665735, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757616740665803, "dur": 204342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740498234, "dur": 15778, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740514020, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_CF2CFC5659289691.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1757616740514522, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740514912, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_179FF29DFFEB2DFF.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1757616740515224, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740515557, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_0671727C8F34DE70.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1757616740515797, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740516093, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_4352508B6FF58D8C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1757616740516330, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740516583, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1757616740516844, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740517162, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740517453, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740517743, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740518028, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740518298, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740518593, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740518944, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740519203, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740519448, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740519673, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740519971, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740520232, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740520490, "dur": 5049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740525540, "dur": 4678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740530219, "dur": 4645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740535556, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Data\\Nodes\\Math\\Round\\CeilingNode.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757616740534864, "dur": 5251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740540115, "dur": 4517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740544632, "dur": 4664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740549296, "dur": 5074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740554370, "dur": 4641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740559012, "dur": 4854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740563867, "dur": 4914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740568782, "dur": 4870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740573653, "dur": 4546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740578199, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740578925, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740579627, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740580271, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1757616740580605, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1757616740581303, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740581584, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740581655, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740581804, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740582246, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740582446, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1757616740582627, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740582715, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1757616740582958, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740583121, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740583833, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740583963, "dur": 72774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740656746, "dur": 5553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1757616740662300, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740662625, "dur": 2697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1757616740665323, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757616740665459, "dur": 204812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740498253, "dur": 15751, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740514005, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8744C5E4C9A4DD73.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757616740514283, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740514664, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_6D8DF5532C44F36E.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757616740515060, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740515386, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_0CAFD6296B62E35C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757616740515664, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740516025, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_FCD481F322AD9A4D.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757616740516283, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740516557, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2A5CE0F0450FED37.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757616740516846, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740517170, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757616740517221, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740517496, "dur": 52377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1757616740569874, "dur": 723, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740570620, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740570881, "dur": 4793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740575675, "dur": 3175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740578919, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740579632, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740580253, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757616740580690, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740580791, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757616740580853, "dur": 958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1757616740581812, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740582141, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740582246, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740582506, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740583102, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757616740583243, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740583310, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1757616740583620, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740583786, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 24, "ts": 1757616740584108, "dur": 120, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740584693, "dur": 59222, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 24, "ts": 1757616740654984, "dur": 1620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1757616740656606, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740656737, "dur": 1844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1757616740658582, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740658673, "dur": 2602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cinemachine.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1757616740661276, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740661556, "dur": 3096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1757616740664653, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740664771, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740664823, "dur": 74759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740739679, "dur": 18483, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 24, "ts": 1757616740739587, "dur": 19635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1757616740760321, "dur": 117, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757616740761016, "dur": 68270, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1757616740861040, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 24, "ts": 1757616740861010, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 24, "ts": 1757616740861212, "dur": 1773, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 24, "ts": 1757616740862992, "dur": 7137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740498315, "dur": 15669, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740513985, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_643AE52642269893.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1757616740514183, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740514494, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_457E63D8633A23E8.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1757616740514866, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740515259, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_5779B10D07596860.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1757616740515604, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740515937, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_3D5E5D08470A5047.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1757616740516209, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740516452, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740516688, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_E4D4DBB35D3233E6.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1757616740516927, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740517231, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_77BE30E9EA33DCEC.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1757616740517542, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740517871, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740518135, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740518400, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740518782, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740519041, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740519264, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740519519, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740519743, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740520029, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740520282, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740520508, "dur": 5054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740525562, "dur": 4811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740530373, "dur": 4661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740535034, "dur": 5075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740540109, "dur": 4589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740544699, "dur": 4569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740549268, "dur": 5169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740554437, "dur": 4699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740559136, "dur": 4819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740563955, "dur": 4964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740568919, "dur": 4808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740573728, "dur": 4746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740578475, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740578932, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740579653, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740580257, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1757616740580688, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1757616740581182, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740581525, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740581635, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740581806, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740581946, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740582314, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740582466, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740583157, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740583831, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740583957, "dur": 72873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740656841, "dur": 3474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1757616740660315, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740660772, "dur": 4092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1757616740664864, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740665029, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757616740665100, "dur": 205239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740498295, "dur": 15767, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740514063, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_6F863C093CE677D7.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1757616740514438, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740514853, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_675E605BA091B10B.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1757616740515113, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740515535, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_A08E0EF719DC53C6.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1757616740515784, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740516092, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_2093BF8DCCEA7764.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1757616740516329, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740516571, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1757616740516796, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740517090, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740517351, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740517661, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740517942, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740518214, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740518496, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740518860, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740519086, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740519325, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740519565, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740519833, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740520161, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740520421, "dur": 4761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740525183, "dur": 4823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740530007, "dur": 4572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740537359, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f8b69e83dfdd\\Editor\\Generation\\Collections\\KernelCollection.cs"}}, {"pid": 12345, "tid": 26, "ts": 1757616740534580, "dur": 5363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740539944, "dur": 4579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740544524, "dur": 4584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740549108, "dur": 5205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740554313, "dur": 4725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740559038, "dur": 4819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740563857, "dur": 4960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740568818, "dur": 4894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740573712, "dur": 4818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740578530, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740578902, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740579614, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740580138, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1757616740580269, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740580399, "dur": 1326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1757616740581726, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740582165, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740582312, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740582459, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740583140, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740583234, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740583796, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740583944, "dur": 71039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740654985, "dur": 1604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1757616740656589, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740656812, "dur": 2887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1757616740659700, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740659835, "dur": 4275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Splines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1757616740664110, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740664466, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740664528, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740665281, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757616740665376, "dur": 204917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740498272, "dur": 15727, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740514000, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_50617412AA022068.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757616740514214, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740514530, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_4B65130BE921D28D.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757616740514922, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740515289, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_6D4EFE421ED4BD41.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757616740515568, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740515812, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_83EB9804E9ECF17E.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757616740516125, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740516369, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_53BA963B625E76F8.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757616740516598, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740516817, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740517119, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740517394, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740517683, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740517956, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740518224, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740518509, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740518896, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740519132, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740519362, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740519599, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740519841, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740520128, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740520379, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740520599, "dur": 4154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740524754, "dur": 4789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740529544, "dur": 4613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740534158, "dur": 5065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740539224, "dur": 4541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740543765, "dur": 4515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740548280, "dur": 5264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740553545, "dur": 4699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740558244, "dur": 4724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740562968, "dur": 4878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740567847, "dur": 4880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740572727, "dur": 4755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740577483, "dur": 1591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740579074, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740579611, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740580208, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757616740580403, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740580512, "dur": 958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1757616740581471, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740581839, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740581974, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740582250, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740582472, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740583105, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757616740583292, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1757616740583584, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740583727, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740583786, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740583935, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757616740584039, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1757616740584418, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740584631, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740584698, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757616740584823, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1757616740585174, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740585366, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757616740585481, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1757616740585718, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740585852, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757616740585943, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1757616740586235, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740586360, "dur": 68670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740655043, "dur": 3763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1757616740658806, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740659023, "dur": 4568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1757616740663592, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740663790, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740663888, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740664025, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740664188, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740664391, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740664653, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757616740665464, "dur": 204784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740498333, "dur": 15645, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740513979, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_780BA9D33BEEE4BA.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757616740514189, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740514518, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_09D67A2A50A62BEC.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757616740514882, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740515209, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_0C3D33FE1383BE43.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757616740515526, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740515810, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4D850B79E83B4A3C.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757616740516139, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740516375, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_6D68E21CF9755271.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757616740516621, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740516892, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740517199, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740517467, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740517790, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740518059, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740518334, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740518671, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740518980, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740519217, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740519462, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740519674, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740520003, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740520308, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740520551, "dur": 4930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740525481, "dur": 4662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740530143, "dur": 4672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740534815, "dur": 5141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740539957, "dur": 4573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740544530, "dur": 4628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740549159, "dur": 5135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740554294, "dur": 4683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740558977, "dur": 4804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740563781, "dur": 4947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740568729, "dur": 4940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740573681, "dur": 4699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740578380, "dur": 509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740578890, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740579610, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740580136, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757616740580265, "dur": 1620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1757616740581885, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740582286, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740582465, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757616740582602, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1757616740583587, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740583793, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757616740583969, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1757616740584664, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740584801, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757616740584897, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1757616740585127, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740585275, "dur": 69791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740655078, "dur": 3550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1757616740658629, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740658768, "dur": 4662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1757616740663431, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740663609, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740663689, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740663825, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740663923, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740663977, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740664146, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740664266, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740664492, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740664849, "dur": 204992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757616740869903, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 28, "ts": 1757616740869843, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1757616740877390, "dur": 1910, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 34412, "tid": 3777, "ts": 1757616740912024, "dur": 3041, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 34412, "tid": 3777, "ts": 1757616740915165, "dur": 2030, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 34412, "tid": 3777, "ts": 1757616740889799, "dur": 28021, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
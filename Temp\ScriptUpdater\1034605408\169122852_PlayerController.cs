using UnityEngine;

public class PlayerController : MonoBehaviour
{
    [Header("Movement")]
    public float moveSpeed = 5f;
    public float dodgeSpeed = 10f;
    public float dodgeDuration = 0.3f;
    
    [Header("Attack")]
    public float attackRange = 2f;
    public float attackDamage = 10f;
    public float attackCooldown = 0.5f;
    
    private Rigidbody rb;
    private bool isDodging = false;
    private bool canAttack = true;
    private float dodgeTimer = 0f;
    private float attackTimer = 0f;
    
    void Start()
    {
        rb = GetComponent<Rigidbody>();
    }
    
    void Update()
    {
        HandleInput();
        UpdateTimers();
    }
    
    void HandleInput()
    {
        if (!isDodging)
        {
            // 이동
            float horizontal = Input.GetAxis("Horizontal");
            float vertical = Input.GetAxis("Vertical");
            Vector3 movement = new Vector3(horizontal, 0, vertical).normalized;
            
            if (movement.magnitude > 0)
            {
                transform.rotation = Quaternion.LookRotation(movement);
                rb.MovePosition(transform.position + movement * moveSpeed * Time.deltaTime);
            }
            
            // 회피 (Space)
            if (Input.GetKeyDown(KeyCode.Space) && movement.magnitude > 0)
            {
                StartDodge(movement);
            }
        }
        
        // 공격 (마우스 좌클릭)
        if (Input.GetMouseButtonDown(0) && canAttack)
        {
            Attack();
        }
    }
    
    void StartDodge(Vector3 direction)
    {
        isDodging = true;
        dodgeTimer = dodgeDuration;
        rb.linearVelocity = direction * dodgeSpeed;
    }
    
    void Attack()
    {
        canAttack = false;
        attackTimer = attackCooldown;
        
        // 공격 범위 내 적 탐지
        Collider[] enemies = Physics.OverlapSphere(transform.position + transform.forward * attackRange/2, attackRange/2);
        
        foreach (Collider enemy in enemies)
        {
            if (enemy.CompareTag("Enemy"))
            {
                // 적에게 데미지 적용 (Enemy 스크립트가 있다면)
                Debug.Log($"공격! {enemy.name}에게 {attackDamage} 데미지");
            }
        }
    }
    
    void UpdateTimers()
    {
        if (isDodging)
        {
            dodgeTimer -= Time.deltaTime;
            if (dodgeTimer <= 0)
            {
                isDodging = false;
                rb.linearVelocity = Vector3.zero;
            }
        }
        
        if (!canAttack)
        {
            attackTimer -= Time.deltaTime;
            if (attackTimer <= 0)
            {
                canAttack = true;
            }
        }
    }
    
    void OnDrawGizmosSelected()
    {
        // 공격 범위 시각화
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position + transform.forward * attackRange/2, attackRange/2);
    }
}
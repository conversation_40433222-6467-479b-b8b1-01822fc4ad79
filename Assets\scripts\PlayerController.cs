using UnityEngine;
using UnityEngine.InputSystem;

public class PlayerController : MonoBehaviour
{
    [Header("Movement")]
    public float moveSpeed = 5f;
    public float jumpForce = 8f;
    public float dodgeSpeed = 10f;
    public float dodgeDuration = 0.3f;
    
    [Header("Attack")]
    public float attackRange = 2f;
    public float attackDamage = 10f;
    public float attackCooldown = 0.5f;
    
    [Header("Ground Check")]
    public LayerMask groundLayer = 1;
    public float groundCheckDistance = 0.1f;
    
    private Rigidbody rb;
    private bool isDodging = false;
    private bool canAttack = true;
    private bool isGrounded = false;
    private float dodgeTimer = 0f;
    private float attackTimer = 0f;
    
    private Vector2 moveInput;
    
    void Start()
    {
        rb = GetComponent<Rigidbody>();
        
        var playerInput = GetComponent<PlayerInput>();
        if (playerInput == null)
        {
            Debug.LogError("PlayerInput component not found!");
        }
        else
        {
            Debug.Log($"PlayerInput found. Actions: {playerInput.actions}");
            // 액션 맵 활성화 확인
            var playerActionMap = playerInput.actions.FindActionMap("Player");
            if (playerActionMap != null)
            {
                Debug.Log($"Player action map found. Enabled: {playerActionMap.enabled}");
            }
        }
    }
    
    void Update()
    {
        CheckGrounded();
        HandleMovement();
        UpdateTimers();
    }
    
    void CheckGrounded()
    {
        isGrounded = Physics.Raycast(transform.position, Vector3.down, 
            GetComponent<Collider>().bounds.extents.y + groundCheckDistance, groundLayer);
    }
    
    void HandleMovement()
    {
        Debug.Log($"HandleMovement - isDodging: {isDodging}, moveInput: {moveInput}");
        if (!isDodging && moveInput.magnitude > 0)
        {
            Vector3 movement = new Vector3(moveInput.x, 0, moveInput.y).normalized;
            transform.rotation = Quaternion.LookRotation(movement);
            rb.MovePosition(transform.position + movement * moveSpeed * Time.deltaTime);
            Debug.Log($"Moving to: {transform.position}");
        }
    }
    
    public void Move(InputAction.CallbackContext context)
    {
        moveInput = context.ReadValue<Vector2>();
        Debug.Log($"Move Input: {moveInput}, Phase: {context.phase}");
    }
    
    public void Jump(InputAction.CallbackContext context)
    {
        if (context.performed && isGrounded)
        {
            PerformJump(); // 메서드 이름 변경
        }
    }
    
    public void Dodge(InputAction.CallbackContext context)
    {
        if (context.performed && moveInput.magnitude > 0 && isGrounded && !isDodging)
        {
            Vector3 direction = new Vector3(moveInput.x, 0, moveInput.y).normalized;
            StartDodge(direction);
        }
    }
    
    public void Attack(InputAction.CallbackContext context)
    {
        if (context.performed && canAttack)
        {
            PerformAttack(); // 메서드 이름 변경
        }
    }
    
    void PerformJump() // 기존 Jump() 메서드 이름 변경
    {
        rb.AddForce(Vector3.up * jumpForce, ForceMode.Impulse);
    }
    
    void StartDodge(Vector3 direction)
    {
        isDodging = true;
        dodgeTimer = dodgeDuration;
        rb.linearVelocity = new Vector3(direction.x * dodgeSpeed, rb.linearVelocity.y, direction.z * dodgeSpeed);
    }
    
    void PerformAttack() // 기존 Attack() 메서드 이름 변경
    {
        canAttack = false;
        attackTimer = attackCooldown;
        
        Collider[] enemies = Physics.OverlapSphere(transform.position + transform.forward * attackRange/2, attackRange/2);
        
        foreach (Collider enemy in enemies)
        {
            if (enemy.CompareTag("Enemy"))
            {
                Debug.Log($"공격! {enemy.name}에게 {attackDamage} 데미지");
            }
        }
    }
    
    void UpdateTimers()
    {
        if (isDodging)
        {
            dodgeTimer -= Time.deltaTime;
            if (dodgeTimer <= 0)
            {
                isDodging = false;
            }
        }
        
        if (!canAttack)
        {
            attackTimer -= Time.deltaTime;
            if (attackTimer <= 0)
            {
                canAttack = true;
            }
        }
    }
    
    void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position + transform.forward * attackRange/2, attackRange/2);
        
        Gizmos.color = Color.green;
        float checkDistance = GetComponent<Collider>() ? 
            GetComponent<Collider>().bounds.extents.y + groundCheckDistance : 1f;
        Gizmos.DrawRay(transform.position, Vector3.down * checkDistance);
    }
}





